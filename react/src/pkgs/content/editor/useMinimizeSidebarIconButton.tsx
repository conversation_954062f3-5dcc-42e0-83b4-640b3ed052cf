import { colours } from '@/common/colours'
import CustomIconButton from '@/common/components/CustomIconButton'
import { useCallback, useEffect, useState, useRef } from 'react'
import FirstPageIcon from '@mui/icons-material/FirstPage'
import LastPage from '@mui/icons-material/LastPage'
import { useStateWithStorage } from '@/common/storage.service'

interface MinimizeSidebarIconButtonProps {
    anchorElement: HTMLElement | undefined | null
}

export default function useMinimizeSidebarIconButton({ anchorElement }: MinimizeSidebarIconButtonProps) {
    const [sidebarIsMinimized, setSidebarIsMinimized] = useStateWithStorage(
        'content-editor-sidebar-is-minimized',
        false
    )

    const [buttonLeftPosition, setButtonLeftPosition] = useState('0px')
    const lastPositionRef = useRef<number>(0)

    // Update buttonLeftPosition whenever anchorElement or sidebarIsMinimized changes
    useEffect(() => {
        const updateButtonLeftPosition = () => {
            if (!anchorElement) return

            const currentPosition = anchorElement.getBoundingClientRect().right

            // Only update if position actually changed
            if (currentPosition !== lastPositionRef.current) {
                lastPositionRef.current = currentPosition
                const leftPositionPx = currentPosition - 8
                setButtonLeftPosition(`${leftPositionPx}px`)
            }
        }

        updateButtonLeftPosition()

        if (!anchorElement) {
            return
        }

        // Use ResizeObserver to monitor the anchor element's size changes
        const resizeObserver = new ResizeObserver(() => {
            updateButtonLeftPosition()
        })

        // Use MutationObserver to monitor broader DOM changes that might affect position
        const mutationObserver = new MutationObserver(() => {
            // Use requestAnimationFrame to ensure DOM changes are complete
            requestAnimationFrame(() => {
                updateButtonLeftPosition()
            })
        })

        // Polling fallback to catch position changes that observers might miss
        const pollInterval = setInterval(() => {
            updateButtonLeftPosition()
        }, 100) // Check every 100ms

        // Start observing the anchor element
        resizeObserver.observe(anchorElement)

        // Observe changes on the document body to catch global sidebar changes
        mutationObserver.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['style', 'class', 'data-sidebar-minimized', 'data-sidebar-open']
        })

        // Also observe the anchor element itself for direct changes
        mutationObserver.observe(anchorElement, {
            attributes: true,
            attributeFilter: ['style', 'class']
        })

        return () => {
            resizeObserver.disconnect()
            mutationObserver.disconnect()
            clearInterval(pollInterval)
        }
    }, [anchorElement, sidebarIsMinimized])

    const MinimizeSidebarIconButton = useCallback(() => {
        return (
            <CustomIconButton
                size='small'
                round
                onClick={() => setSidebarIsMinimized(!sidebarIsMinimized)}
                sx={{
                    fontSize: '4px',
                    backgroundColor: colours.base_blue,
                    color: colours.white,
                    border: `1px solid ${colours.off_white_but_darker}`,
                    zIndex: 1,
                    position: 'fixed',
                    marginTop: `160px`,
                    left: buttonLeftPosition,
                    '.MuiSvgIcon-root': {
                        fontSize: '18px'
                    }
                }}
            >
                {sidebarIsMinimized ? <FirstPageIcon /> : <LastPage />}
            </CustomIconButton>
        )
    }, [buttonLeftPosition, setSidebarIsMinimized, sidebarIsMinimized])

    return { sidebarIsMinimized, MinimizeSidebarIconButton }
}
