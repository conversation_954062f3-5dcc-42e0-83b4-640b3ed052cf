import { Item, List } from './types'
import { useContentDetailsQuery } from '../content/queries'
import { Box, DialogActions, DialogContent, FormControlLabel, Grid, Switch, TextField, Typography } from '@mui/material'
import React from 'react'
import FormRenderer from '../form-renderer/FormRenderer'
import { notify } from '../../helpers'
import { DateTimeNullable } from '@/common/components/dates/DateTimeNullable'
import { ImagePreview, MediaIDSelector } from '../media/image/MediaIDSelector'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import { ContentType } from '../content/types'
import { LoadingButton } from '@mui/lab'
import CMDialog from '@/common/components/CMDialog'
import { colours } from '@/common/colours'

function ContentSectionRow({ columns }: { columns: [JSX.Element, JSX.Element, JSX.Element] }) {
    const sizing = [1.5, 8.5, 1.5]
    return (
        <Grid item container md={12} display='flex' justifyContent='space-between'>
            <Grid item container md={sizing[0]} justifyContent='flex-start'>
                {columns[0]}
            </Grid>
            <Grid item container md={sizing[1]}>
                {columns[1]}
            </Grid>
            <Grid item container md={sizing[2]} justifyContent='flex-end'>
                {columns[2]}
            </Grid>
        </Grid>
    )
}

const defaultOverrides: Record<string, any> = {}

type ItemEditorProps = {
    item: Item
    onSave: (item: Item) => void
    onCancel: () => void
    list: Pick<List, 'Structure' | 'ContentTypes' | 'OverrideSections'>
    saving?: boolean
}

export function ItemEditor({ item, onSave, onCancel, list, saving }: ItemEditorProps) {
    const isContentLike = !list.ContentTypes.includes(ContentType.Fragment)
    item.Overrides = item.Overrides || defaultOverrides

    const [activeOverrides, setActiveOverrides] = React.useState<string[]>(
        item.Overrides ? Object.keys(item.Overrides) : []
    )
    const [open, setOpen] = React.useState(!!item)
    const [state, setState] = React.useState(item)
    const formRendererRefs = React.useRef<any[]>([])

    const result = useContentDetailsQuery(item.ContentID, 'live')

    React.useEffect(() => {
        formRendererRefs.current =
            list.Structure?.FormStructure.map((_, i) => formRendererRefs.current[i] ?? React.createRef()) || []
    }, [list])

    const canBeOverridden = (key: string) => {
        return list.OverrideSections?.includes(key)
    }

    const getDefaultValue = (key: string) => {
        switch (key) {
            case 'ContentTitle':
                return result?.data?.Title || ''
            case 'ContentExpireAt':
                return result?.data?.ExpireAt || null
            case 'ContentDescription':
                return result?.data?.Settings?.seoDescription || ''
            case 'ContentMedia':
                return result?.data?.MediaID || null
            default:
                if (result?.data?.Data && result.data.Data[key]) return result?.data?.Data[key]

                const allowMultiple = !!list?.Structure?.FormStructure.find((s) => s.name === key)?.allowMultiple
                return allowMultiple ? [] : {}
        }
    }
    const toggleOverride = (key: string) => {
        if (activeOverrides.includes(key)) {
            setActiveOverrides(activeOverrides.filter((o) => o !== key))
            const newOverrides = { ...state.Overrides }
            delete newOverrides[key]
            setState({
                ...state,
                Overrides: newOverrides
            })
        } else {
            setActiveOverrides([...activeOverrides, key])
            setState({
                ...state,
                Overrides: {
                    ...state.Overrides,
                    [key]: getDefaultValue(key)
                }
            })
        }
    }

    return (
        <CMDialog
            showCloseButton
            open={open}
            onClose={onCancel}
            fullWidth
            maxWidth={'xl'}
            title={
                <Box
                    display='flex'
                    flexDirection='row'
                    justifyContent='space-between'
                    alignItems='center'
                    paddingRight={'1.2rem'}
                >
                    <Box>
                        <Typography variant='h6' sx={{ display: 'flex', flexDirection: 'row' }}>
                            Item Editor
                        </Typography>
                        <Typography fontStyle='italic'>
                            Override how this item shows in the context of this ordered list
                        </Typography>
                    </Box>
                    <Box justifyContent='flex-end' alignItems='flex-end' display='flex' flexDirection='column'>
                        <Box flexDirection='row' display='flex' alignItems='center' gap='4px'>
                            <Typography
                                variant='h6'
                                textTransform='capitalize'
                                sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: '4px' }}
                            >
                                {result.data?.Type || ''}:
                            </Typography>
                            <Typography variant='h6' fontWeight='400'>
                                {result.data?.Title || ''}
                            </Typography>
                        </Box>
                        <Typography variant='caption'>{state.ContentID || ''}</Typography>
                    </Box>
                </Box>
            }
        >
            <DialogContent>
                {!result?.data ? null : (
                    <Grid container item direction='column' marginTop={'1rem'}>
                        <Grid
                            container
                            item
                            direction='column'
                            sx={{
                                border: `1.5px solid ${colours.off_white_but_darker}`,
                                borderRadius: '8px',
                                width: '100%',
                                padding: '1.2rem',
                                height: '100%'
                            }}
                            position='relative'
                            gap={4}
                        >
                            <Typography
                                variant='h6'
                                position='absolute'
                                top={'-15px'}
                                px={'5px'}
                                sx={{ backgroundColor: 'white' }}
                            >
                                Content Sections
                            </Typography>

                            <ContentSectionRow
                                columns={[
                                    <Typography key={'title-label'} variant={'subtitle1'}>
                                        Title
                                    </Typography>,
                                    <TextField
                                        key={'title'}
                                        sx={{ width: '100%' }}
                                        size='small'
                                        onChange={(event) =>
                                            setState({
                                                ...state,
                                                Overrides: {
                                                    ...state.Overrides,
                                                    ContentTitle: event.target.value || ''
                                                }
                                            })
                                        }
                                        disabled={
                                            !(
                                                canBeOverridden('ContentTitle') &&
                                                activeOverrides.includes('ContentTitle')
                                            )
                                        }
                                        value={state.Overrides['ContentTitle'] || result.data.Title || ''}
                                        placeholder={state.Overrides['ContentTitle']}
                                        variant={'outlined'}
                                    />,
                                    <FormControlLabel
                                        key={'title-override'}
                                        control={
                                            <Switch
                                                checked={activeOverrides.includes('ContentTitle')}
                                                onChange={() => toggleOverride('ContentTitle')}
                                            />
                                        }
                                        label={'override'}
                                        labelPlacement={'start'}
                                        disabled={!canBeOverridden('ContentTitle')}
                                        title={!canBeOverridden('ContentTitle') ? 'Cannot be overridden' : undefined}
                                    />
                                ]}
                            />
                            <ContentSectionRow
                                columns={[
                                    <Typography key={'media-label'} variant={'subtitle1'}>
                                        Media
                                    </Typography>,
                                    canBeOverridden('ContentMedia') && activeOverrides.includes('ContentMedia') ? (
                                        <MediaIDSelector
                                            value={state.Overrides['ContentMedia'] || null}
                                            onChange={(id) =>
                                                setState({
                                                    ...state,
                                                    Overrides: {
                                                        ...state.Overrides,
                                                        ContentMedia: id
                                                    }
                                                })
                                            }
                                        />
                                    ) : (
                                        <>
                                            {result.data.MediaID && <ImagePreview id={result.data.MediaID} />}
                                            {!result.data.MediaID && <Typography>No media</Typography>}
                                        </>
                                    ),
                                    <FormControlLabel
                                        key={'media-override'}
                                        control={
                                            <Switch
                                                checked={activeOverrides.includes('ContentMedia')}
                                                onChange={() => toggleOverride('ContentMedia')}
                                            />
                                        }
                                        label={'override'}
                                        labelPlacement={'start'}
                                        disabled={!canBeOverridden('ContentMedia')}
                                        title={!canBeOverridden('ContentMedia') ? 'Cannot be overridden' : undefined}
                                    />
                                ]}
                            />
                            <ContentSectionRow
                                columns={[
                                    <Typography key={'expire-at-label'} variant={'subtitle1'}>
                                        Expire At
                                    </Typography>,
                                    canBeOverridden('ContentExpireAt') &&
                                    activeOverrides.includes('ContentExpireAt') ? (
                                        <DateTimeNullable
                                            size='small'
                                            value={state.Overrides['ContentExpireAt'] || null}
                                            onChange={(date) =>
                                                setState({
                                                    ...state,
                                                    Overrides: {
                                                        ...state.Overrides,
                                                        ContentExpireAt: date
                                                    }
                                                })
                                            }
                                            label={'Expire at'}
                                            min={result.data.PublishAt}
                                            max={result.data.ExpireAt}
                                        />
                                    ) : (
                                        <Typography>
                                            {result.data.ExpireAt?.toString() || 'No expiration date'}
                                        </Typography>
                                    ),
                                    <FormControlLabel
                                        key={'expire-at-override'}
                                        control={
                                            <Switch
                                                checked={activeOverrides.includes('ContentExpireAt')}
                                                onChange={() => toggleOverride('ContentExpireAt')}
                                            />
                                        }
                                        label={'override'}
                                        labelPlacement={'start'}
                                        disabled={!canBeOverridden('ContentExpireAt')}
                                        title={!canBeOverridden('ContentExpireAt') ? 'Cannot be overridden' : undefined}
                                    />
                                ]}
                            />
                        </Grid>
                    </Grid>
                )}
                {result.isLoading && <div>Loading...</div>}
                {result.error && <div>Error: {guessErrorMessage(result.error)}</div>}
                {result.data && (
                    <Grid container item direction='column' marginTop={'3rem'}>
                        <Grid
                            md={12}
                            container
                            item
                            direction='row'
                            sx={{
                                border: `1.5px solid ${colours.off_white_but_darker}`,
                                borderRadius: '8px',
                                width: '100%',
                                padding: '1.2rem'
                            }}
                            position='relative'
                        >
                            <Typography
                                variant='h6'
                                position='absolute'
                                top={'-15px'}
                                px={'5px'}
                                sx={{ backgroundColor: 'white' }}
                            >
                                Structure Sections
                            </Typography>
                            {list.Structure?.FormStructure.map((s, index) => {
                                const key = s.name
                                const label = s.title
                                const isOverridable = canBeOverridden(key)
                                const isOverridden = activeOverrides.includes(key) || isContentLike
                                const toggle = () => toggleOverride(key)
                                return (
                                    <Grid
                                        key={key}
                                        item
                                        md={12}
                                        display='flex'
                                        justifyContent='flex-end'
                                        alignItems='start'
                                        width='100%'
                                    >
                                        <Grid container item md={10.5}>
                                            <FormRenderer
                                                ref={formRendererRefs.current[index]}
                                                value={
                                                    state.Overrides[key]
                                                        ? state.Overrides
                                                        : { ...state.Overrides, [key]: getDefaultValue(key) }
                                                }
                                                onChange={(v) =>
                                                    isOverridden
                                                        ? setState({
                                                              ...state,
                                                              Overrides: { ...state.Overrides, ...v }
                                                          })
                                                        : {}
                                                }
                                                formStructure={[s]}
                                                isFieldRestricted={() => false}
                                                disabled={!isOverridden}
                                            />
                                        </Grid>
                                        <Grid container item md={1.5} justifyContent='flex-end' alignItems='start'>
                                            <FormControlLabel
                                                control={
                                                    <Switch
                                                        checked={isOverridden}
                                                        onChange={toggle}
                                                        disabled={isContentLike}
                                                    />
                                                }
                                                label={'override'}
                                                labelPlacement={'start'}
                                                disabled={!isOverridable || isContentLike}
                                                title={!isOverridable ? 'Cannot be overridden' : undefined}
                                            />
                                        </Grid>
                                    </Grid>
                                )
                            })}
                        </Grid>
                    </Grid>
                )}
            </DialogContent>
            <DialogActions sx={{ borderTop: `1px solid ${colours.off_white_but_darker}` }}>
                <LoadingButton
                    loading={saving}
                    sx={{ mr: 2 }}
                    onClick={() => {
                        setOpen(false)
                        onCancel()
                    }}
                >
                    Cancel
                </LoadingButton>
                <LoadingButton
                    loading={saving}
                    variant={'contained'}
                    onClick={() => {
                        let hasErrors = false
                        formRendererRefs.current.forEach((formRendererRef) => {
                            if (
                                formRendererRef.current &&
                                typeof formRendererRef.current.validateAndReturnErrors === 'function'
                            ) {
                                const [components] = formRendererRef.current.validateAndReturnErrors()
                                if (Array.isArray(components) && components.length) {
                                    hasErrors = true
                                }
                            }
                        })

                        if (hasErrors) {
                            notify('Please fill in all required fields', 'error')
                            return
                        }
                        onSave(state)
                    }}
                >
                    Save changes
                </LoadingButton>
            </DialogActions>
        </CMDialog>
    )
}
