import { Box, Stack, Switch, Typography } from '@mui/material'
import React, { useEffect, useRef } from 'react'
import { primaryTheme } from '@/app/theme'
import { useReservable } from '@/pkgs/reservation/useReservable'
import { SessionTimeout, SessionTimeoutRef } from '@/pkgs/reservation/SessionTimeout'
import { ReservationInfo } from '@/pkgs/reservation/ReservationInfo'

interface ReservationSwitchProps {
    reservable: any
    onChange?: (checked: boolean) => void
    disabled?: boolean
    hideExtendedLock?: boolean
}

export function ReservationSwitch({
    reservable,
    disabled,
    onChange,
    hideExtendedLock = false
}: ReservationSwitchProps) {
    const key = `content::${reservable.ID}::${reservable.Workspace}`
    const { loading, value, start, end, reservableInfo } = useReservable(key)

    const sessionTimeoutRef = useRef<SessionTimeoutRef>(null)

    useEffect(() => {
        onChange?.(value ?? false)
    }, [value])

    useEffect(() => {
        console.log('Reservation switch mounted for key:', key)
        sessionTimeoutRef?.current?.reset()
    }, [reservable])

    return (
        <>
            <Box>
                <Stack direction='row' component='label' alignItems='center' justifyContent='center'>
                    <Typography>Read</Typography>
                    <Switch
                        checked={value ?? false}
                        onChange={(e, checked) => (checked ? start() : end())}
                        style={{
                            color: value ? primaryTheme.palette.primary.main : primaryTheme.palette.warning.main
                        }}
                        disabled={loading}
                    />
                    <Typography>Edit</Typography>
                    {!hideExtendedLock && (
                        <ReservationInfo reservableInfo={reservableInfo} reservationKey={key} isActive={value} />
                    )}
                </Stack>
            </Box>

            <SessionTimeout
                ref={sessionTimeoutRef}
                isActive={value}
                onTimeout={() => {
                    end()
                }}
            />
        </>
    )
}
