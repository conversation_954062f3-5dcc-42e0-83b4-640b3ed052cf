import { Content } from '@/pkgs/content/types'
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ack, Typography } from '@mui/material'
import { ReservationSwitch } from '@/pkgs/reservation/ReservationSwitch'
import { useState } from 'react'
import { httpPost, httpPut } from '@/common/client'
import { BASE } from '@/common/constants'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import { notify } from '@/helpers'

type PublishToLiveProps<T extends Content> = {
    value: T
    onPublish: () => void
    liveExists: boolean
}

export function PublishToLive<T extends Content>({ value, onPublish, liveExists }: PublishToLiveProps<T>) {
    const [liveIsAvailable, setLiveIsAvailable] = useState(!liveExists)
    const [isPublishing, setIsPublishing] = useState(false)
    const [error, setError] = useState<string | null>(null)

    // /api/v2/content/:id/:workspace
    const publish = () => {
        setIsPublishing(true)
        httpPost(`${BASE}/api/v2/content/${value.ID}/${value.Workspace}`, null)
            .then(() => {
                notify('Successfully published to live', 'info')
                onPublish()
            })
            .catch((err) => {
                setError(guessErrorMessage(err))
            })
            .finally(() => setIsPublishing(false))
    }

    return (
        <Stack direction='column' spacing={2}>
            {liveExists && (
                <Stack direction={'row'} spacing={2}>
                    {liveIsAvailable && (
                        <Alert severity={'info'}>Live is available for editing, you can publish.</Alert>
                    )}
                    {!liveIsAvailable && (
                        <Alert severity={'warning'}>
                            Live is not available for editing. To publish you need to switch `live` to editing mode.
                        </Alert>
                    )}
                    <ReservationSwitch
                        reservable={{ ...value, Workspace: 'live' }}
                        onChange={setLiveIsAvailable}
                        hideExtendedLock={true}
                    />
                </Stack>
            )}

            <Typography variant={'subtitle1'}>
                You are about to publish <strong>"{value.Title}"</strong> to live. Please confirm that you want to do
                this.
            </Typography>
            <Button variant={'contained'} color={'success'} onClick={publish} disabled={!liveIsAvailable}>
                Publish to Live
            </Button>
            <Divider orientation={'horizontal'} />
            <Button onClick={onPublish}>Cancel</Button>
        </Stack>
    )
}
