package ietags

import (
	"contentmanager/infrastructure/database/pgxx"
	"contentmanager/library/shared"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/logging"
	"fmt"
	"net/url"
	"regexp"
	"strings"
)

var (
	lexicalDocumentLinksRegex = regexp.MustCompile(`(?s)<ie-document[^>]*data-lexical-document-id="([0-9a-fA-F-]+)"[^>]*>.*?</ie-document>`)
)

func ReplaceLexicalDocumentLinkNodes(r *shared.AppContext, content string) string {
	matches := lexicalDocumentLinksRegex.FindAllStringSubmatch(content, -1)
	var documentIds = []string{}
	var idToDocument = map[string]commonModels.Document{}
	for _, match := range matches {
		if len(match) != 2 {
			continue
		}
		documentId := match[1]
		documentIds = append(documentIds, documentId)
	}

	if len(documentIds) == 0 {
		return content
	}

	var documents []commonModels.Document
	err := r.TenantDatabase().
		Where(pgxx.FieldInArray("id", documentIds)).
		Where("active").
		Find(&documents).Error
	if err != nil {
		logging.RootLogger().
			Err(err).
			Msg("[ReplaceLexicalDocumentLinkNodes] failed to get documents by id")
	}
	// if there is an error, then documents.len == 0, no need to manage differently.
	for _, document := range documents {
		idToDocument[document.ID.String()] = document
	}
	privacyLevel := r.Account().PrivacyLevel
	for _, match := range matches {
		if len(match) != 2 {
			continue
		}
		lexicalDocumentLinkNodeHTML := match[0]
		documentId := match[1]

		document, found := idToDocument[documentId]
		if !found {
			content = strings.ReplaceAll(content, lexicalDocumentLinkNodeHTML, fmt.Sprintf("<!-- [Err] didn't find documentId: %s -->", documentId))
			continue
		}
		if document.PrivacyLevel > privacyLevel {
			// Mismatched privacy level silently skips, no error.
			content = strings.ReplaceAll(content, lexicalDocumentLinkNodeHTML, "")
			continue
		}
		var path string
		if document.Type == commonModels.File {
			path = "/documents/" + document.ID.String() + "/" + url.PathEscape(document.Filename)
		} else {
			path = "/folder/" + document.ID.String() + "/" + url.PathEscape(document.Filename)
		}
		
		var replaceValue string
		if len(document.Title) > 0 {
			replaceValue = document.Title
		} else {
			replaceValue = document.Filename
		}
		content = strings.ReplaceAll(content, lexicalDocumentLinkNodeHTML, getAnchorTagForDocumentLink(path, replaceValue))

	}
	return content
}

func getAnchorTagForDocumentLink(href string, innerHTML string) string {
	return fmt.Sprintf(`<a href="%s" target="_blank">%s</a>`, href, innerHTML)
}
