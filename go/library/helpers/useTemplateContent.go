package helpers

import (
	"contentmanager/infrastructure/database/pgxx"
	commonModels "contentmanager/library/tenant/common/models"
	handlebarsModels "contentmanager/library/tenant/public/models"
	"contentmanager/library/tenant/public/utils/handlebars"
)

func init() {
	handlebars.RegisterHelper("useTemplateContent", useTemplateContent)
}

func useTemplateContent(templates []handlebarsModels.ContentForHandlebars, template string, options *handlebars.Options) interface{} {
	appContext, err := handlebars.GetAppContextFromOptions(options)
	if err != nil {
		return ""
	}

	var foundTemplate handlebarsModels.ContentForHandlebars
	query := appContext.TenantDatabase().Model(&foundTemplate)
	if err := query.
		Where("active").
		Where("type = ?", commonModels.Template).
		Where(pgxx.ArrayHasAny("sites", []string{appContext.CurrentSiteID().String()})).
		Where(pgxx.Workspace("live", "")).
		Where("title = ?", template).Find(&foundTemplate).Error; err != nil {
		return ""
	}

	return foundTemplate.Content

}
