package db_models

import (
	"contentmanager/infrastructure/database/driver"
	"contentmanager/library/shared"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/library/utils"
	"contentmanager/library/utils/converters"
	"contentmanager/pkgs/auth"
	"contentmanager/pkgs/content/events"
	"encoding/json"
	"fmt"
	"time"

	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

type (
	Content struct {
		commonModels.Entity
		auth.SharableBase
		PublishAt    *time.Time
		ExpireAt     *time.Time
		PrivacyLevel int
		Type         string
		Title        string
		DataRaw      json.RawMessage `gorm:"column:data;type:jsonb;DEFAULT:'{}'::jsonb;NOT NULL"`
		Route        string
		Path         string
		CreatedBy    uuid.UUID `gorm:"column:owner;type:uuid"`
		UpdatedBy    uuid.UUID `gorm:"column:publisher;type:uuid"`
		CreatedAt    time.Time `gorm:"column:created;type:timestamp with time zone;DEFAULT:now()"`
		UpdatedAt    time.Time `gorm:"column:updated;type:timestamp with time zone"`
		MediaID      *uuid.UUID
		SettingsRaw  json.RawMessage `gorm:"column:settings;type:jsonb;DEFAULT:'{}'::jsonb;NOT NULL"`
		StructureID  *uuid.UUID
		MetaRaw      json.RawMessage        `gorm:"column:meta;type:jsonb;DEFAULT:'{}'::jsonb;NOT NULL"`
		Tags         driver.PgUUIDArray     `gorm:"type:uuid[]"`
		StartDate    *time.Time             `gorm:"-"`
		EndDate      *time.Time             `gorm:"-"`
		Event        *events.Schedule       `gorm:"-"`
		IsAllDay     bool                   `gorm:"-"`
		Data         map[string]interface{} `gorm:"-"`
		Meta         map[string]interface{} `gorm:"-"`
		Settings     map[string]interface{} `gorm:"-"`
	}
)

var _ Linkable = (*Content)(nil)

func (c Content) ExtendedData() []byte {
	return c.DataRaw
}

var _ Extensible = (*Content)(nil)

func (Content) TableName() string {
	return "content"
}

func (c Content) EventSchedule(r *shared.AppContext) events.Schedule {
	var schedule events.Schedule
	if err := json.Unmarshal(c.SettingsRaw, &schedule); err != nil {
		return schedule
	}
	schedule.Loc = r.TimezoneLocation()
	return schedule
}

func (c Content) Link(r *shared.AppContext) string {
	if c.Type == "external_link" {
		return c.Route
	}
	bestSite := r.BestSite(c.Sites)
	if c.Type == "fragment" {
		return fmt.Sprintf("https://%s/api/v1/fragments/%s/compile", bestSite.PrimaryDomain, c.ID)
	}

	return fmt.Sprintf("https://%s%s", bestSite.PrimaryDomain, c.Route)
}

func (c Content) BestSite(r *shared.AppContext) string {
	return fmt.Sprintf("https://%s", r.BestSite(c.Sites).PrimaryDomain)
}

func (c *Content) AfterFind(tx *gorm.DB) error {
	r, ok := tx.Statement.Context.Value("app_context").(*shared.AppContext)
	if !ok {
		return fmt.Errorf("app_context not found in context")
	}
	c.CreatedAt = c.CreatedAt.In(r.TimezoneLocation())
	c.UpdatedAt = c.UpdatedAt.In(r.TimezoneLocation())
	if c.PublishAt != nil {
		localTime := c.PublishAt.In(r.TimezoneLocation())
		c.PublishAt = &localTime
	}

	if c.ExpireAt != nil {
		localTime := c.ExpireAt.In(r.TimezoneLocation())
		c.ExpireAt = &localTime
	}

	if c.Type == "event" {
		c.ProcessSchedule(r)
	}

	if c.DataRaw != nil {
		c.Data = utils.BytesToMapStringInterface(c.DataRaw)
	}
	if c.MetaRaw != nil {
		c.Meta = utils.BytesToMapStringInterface(c.MetaRaw)
	}
	if c.SettingsRaw != nil {
		c.Settings = utils.BytesToMapStringInterface(c.SettingsRaw)
	}

	return nil
}

func (c *Content) ProcessSchedule(r *shared.AppContext) {
	schedule := c.EventSchedule(r)
	c.IsAllDay = schedule.IsAllDay
	if schedule.IsAllDay {
		// For grouping purposes, we need to convert the UTC dates to the local timezone
		startInLoc := time.Date(
			schedule.StartDate.UTC().Year(),
			schedule.StartDate.UTC().Month(),
			schedule.StartDate.UTC().Day(),
			0, 0, 0, 0,
			r.TimezoneLocation(),
		)
		endInLoc := time.Date(
			schedule.EndDate.UTC().Year(),
			schedule.EndDate.UTC().Month(),
			schedule.EndDate.UTC().Day(),
			0, 0, 0, 0,
			r.TimezoneLocation(),
		)
		c.StartDate = &startInLoc
		c.EndDate = &endInLoc
	} else {
		c.StartDate = converters.AsPointer(schedule.StartDate.In(r.TimezoneLocation()))
		c.EndDate = converters.AsPointer(schedule.EndDate.In(r.TimezoneLocation()))
	}
	c.Event = &schedule
}
