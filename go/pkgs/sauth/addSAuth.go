package sauth

import (
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	"contentmanager/library/utils"
	"contentmanager/library/utils/urls"
	"contentmanager/pkgs/auth/identity"
	"contentmanager/pkgs/auth/login/social"
	"contentmanager/pkgs/auth/token"
	"contentmanager/pkgs/config"
	"contentmanager/pkgs/multitenancy"
	"contentmanager/pkgs/sauth/providers/twitterstate"
	"contentmanager/pkgs/sauth/providers/yahoo"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"github.com/markbates/goth"
	"github.com/markbates/goth/gothic"
	"github.com/markbates/goth/providers/google"
	"github.com/markbates/goth/providers/microsoftonline"
	uuid "github.com/satori/go.uuid"
	"net/http"
	"strings"
)

/*
Direct links to the settings for Google, Microsoft (?), Twitter, Yahoo APIs:
https://console.cloud.google.com/apis/credentials/oauthclient/************-vb7n2t80ib020r6v4v2o398lt1kkr6l6.apps.googleusercontent.com?project=content-manager-305721&supportedpurview=project
https://portal.azure.com/#view/Microsoft_AAD_RegisteredApps/ApplicationMenuBlade/~/Overview/appId/00321350-3030-4eb7-a90d-1af0e738d39d/isMSAApp~/false
https://developer.twitter.com/en/portal/projects/1595920678523666432/apps/26136404/auth-settings (personal twitter acc for @AnatolyUA <<EMAIL>>)
https://developer.yahoo.com/apps/f6ZgA9YL/ (google sign in yahoo dev <NAME_EMAIL>)
*/
const routePrefix = "/sys/auth" // routePrefix should be the same as in the settings for Google, Microsoft, Twitter, Yahoo APIs

type (
	Config struct {
		Host      string
		Providers []Provider
	}
	Provider struct {
		Name   string
		ID     string
		Secret string
	}
)

func AddSAuth(r *httpService.DefaultMiddleware, c Config) *httpService.DefaultMiddleware {
	// samples/docs: https://github.com/markbates/goth/blob/master/examples/main.go
	prefix := strings.Trim(c.Host, "/") + routePrefix
	providers := []goth.Provider{}
	for _, p := range c.Providers {
		switch strings.ToLower(p.Name) {
		case "twitter":
			providers = append(providers, twitterstate.New(p.ID, p.Secret, fmt.Sprintf("%s/twitter/callback", prefix)))
		case "google":
			providers = append(providers, google.New(p.ID, p.Secret, fmt.Sprintf("%s/google/callback", prefix)))
		case "microsoft":
			providers = append(providers, microsoftonline.New(p.ID, p.Secret, fmt.Sprintf("%s/microsoftonline/callback", prefix)))
		case "yahoo":
			providers = append(providers, yahoo.New(p.ID, p.Secret, fmt.Sprintf("%s/yahoo/callback", prefix)))
		}
	}
	goth.UseProviders(providers...)

	r.Get(routePrefix+"/whoami", func(res http.ResponseWriter, r *shared.AppContext) {
		res.Header().Set("Content-Type", "application/json; charset=utf-8")
		res.WriteHeader(http.StatusOK)
		_ = json.NewEncoder(res).Encode(r.PublicAccount())
	})

	r.Get(routePrefix+"/:provider/callback", func(res http.ResponseWriter, r *shared.AppContext, factory multitenancy.AccessorFactory, p httpService.Params, manager token.TokenManager[identity.PublicAccount]) {
		state := r.Request().FormValue("state")
		redirectUrl := "/"
		// TODO => MT: State value parsed here as well.
		if len(state) > 0 {
			if val, err := base64.RawURLEncoding.DecodeString(state); err == nil {
				stateValues := strings.Split(string(val), "|")
				host := stateValues[0]
				if len(stateValues) > 1 {
					redirectUrl = stateValues[2]
				}
				if r.Request().Host != host {
					to := "https://" + host + r.Request().RequestURI
					r.Logger().Info().Str("redirect", to).Send()
					http.Redirect(res, r.Request(), to, http.StatusTemporaryRedirect)
					return
				}
			}
		}

		user, err := gothic.CompleteUserAuth(res, r.Request().WithContext(context.WithValue(r.Request().Context(), "provider", p["provider"])))
		if err != nil {
			r.Logger().Error().Err(err).Send()
			return
		}

		name := user.Name
		if len(name) == 0 {
			name = strings.TrimSpace(fmt.Sprintf("%s %s", user.FirstName, user.LastName))
		}
		if len(name) == 0 {
			name = user.NickName
		}

		if err := social.AuthenticateSocialLogin(r, res, user, manager); err != nil {
			r.Logger().Error().Err(err).Send()
			utils.WriteHTML(res, `<pre>Something went wrong.</pre>`, http.StatusBadRequest)
			return
		}

		http.Redirect(res, r.Request(), redirectUrl, http.StatusSeeOther)
	})

	r.Get(routePrefix+"/logout/:provider", func(res http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
		gothic.Logout(res, r.Request().WithContext(context.WithValue(r.Request().Context(), "provider", p["provider"])))
		res.Header().Set("Location", "/")
		res.WriteHeader(http.StatusTemporaryRedirect)
	})

	r.Get(routePrefix+"/logout", func(res http.ResponseWriter, r *shared.AppContext) {
		//config := cache.ICacheAdapter().GetObject(conf.TenancyConfigCacheKey).(config.AppConfig)
		appConfig := config.GetAppConfig()
		cookie := &http.Cookie{
			Name:     appConfig.CookieName,
			Value:    "",
			Domain:   r.Request().Host,
			Path:     "/",
			Secure:   true,
			SameSite: http.SameSiteNoneMode,
			MaxAge:   -1,
		}
		http.SetCookie(res, cookie)
		res.Header().Set("Location", "/")
		res.WriteHeader(http.StatusTemporaryRedirect)
	})

	r.Get(routePrefix+"/:provider/login", func(res http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
		redirectUrl := r.Request().URL.Query().Get("redirect_url")
		if len(redirectUrl) == 0 {
			redirectUrl = "/"
		}

		if len(r.PublicAccount().Email) > 0 {
			http.Redirect(res, r.Request(), redirectUrl, http.StatusSeeOther)
			return
		}
		pr := r.Request().URL.Query().Get("provider")

		r.Logger().Info().Msg(pr)
		newReq := r.Request().WithContext(context.WithValue(r.Request().Context(), "provider", p["provider"]))
		state := base64.RawURLEncoding.EncodeToString([]byte(r.Request().Host + `|` + uuid.NewV4().String() + `|` + redirectUrl))
		newURL := urls.ReplaceGetParam(newReq.URL, "state", state)
		newReq.URL = newURL
		s := newReq.URL.Query().Get("state")
		r.Logger().Info().Msg(s)
		gothic.BeginAuthHandler(res, newReq)
	})

	return r
}
func MapSocAuthConfig(c config.AppConfig) Config {
	providers := []Provider{
		{
			Name:   "google",
			ID:     c.SocAuthGoogleID,
			Secret: c.SocAuthGoogleSecret,
		},
		{
			Name:   "microsoft",
			ID:     c.SocAuthMicrosoftID,
			Secret: c.SocAuthMicrosoftSecret,
		},
		{
			Name:   "twitter",
			ID:     c.SocAuthTwitterID,
			Secret: c.SocAuthTwitterSecret,
		},
		{
			Name:   "yahoo",
			ID:     c.SocAuthYahooID,
			Secret: c.SocAuthYahooSecret,
		},
	}
	return Config{
		Host:      strings.Trim(c.SocAuthHost, "/"),
		Providers: providers,
	}
}
