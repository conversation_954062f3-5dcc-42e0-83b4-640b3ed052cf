package main

import (
	"context"
	"errors"
	"log"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"strings"
	"syscall"
	"time"
)

const webRoot = "/app/static"
const port = "3000"

func SPAFileServer(fs http.FileSystem, files map[string]interface{}) http.Handler {
	fsh := http.FileServer(fs)

	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Check if the file exists in the map and if not: serve index.html
		if _, ok := files[r.URL.Path]; !ok {
			w.Header().Set("X-IE-SPA-Error", "file not found")
			w.<PERSON>er().Set("Cache-Control", "max-age=0, must-revalidate")
			http.ServeFile(w, r, webRoot+"/index.html")
			return
		}

		fsh.ServeHTTP(w, r)
	})
}

func main() {
	files, err := ListFiles(webRoot)
	if err != nil {
		log.Fatal(err)
	}
	files[""] = struct{}{}

	fs := http.Dir(webRoot)
	http.Handle("/", http.StripPrefix("/", SPAFileServer(fs, files)))

	server := &http.Server{
		Addr: ":" + port,
	}

	// Create channel to listen for OS signals for graceful shutdown
	stop := make(chan os.Signal, 1)
	signal.Notify(stop, os.Interrupt, syscall.SIGTERM)

	go func() {
		log.Println("Listening on :" + port)
		if err := server.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			log.Fatalf("Could not listen on :%s: %v\n", port, err)
		}
	}()

	// Block until we receive our signal.
	<-stop

	// Create a deadline to wait for.
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	log.Println("Shutting down server...")

	// Attempt to gracefully shut down the server.
	if err := server.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	log.Println("Server stopped")
}

func ListFiles(root string) (map[string]interface{}, error) {
	var files = map[string]interface{}{}

	err := filepath.Walk(root, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Skip the root folder itself
		if path == root {
			return nil
		}

		if !info.IsDir() {
			files[strings.TrimPrefix(strings.Replace(strings.Replace(path, "\\", "/", -1), root, "", 1), "/")] = struct{}{}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return files, nil
}
