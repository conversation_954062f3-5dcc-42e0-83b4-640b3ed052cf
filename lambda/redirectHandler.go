package main

import (
	"context"
	"fmt"
	"net/url"
	"strings"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
)

var mappedHosts = map[string]string{
	"redeemer.ab.ca":                 "www.redeemer.ab.ca",
	"albertahomeeducation.ca":        "centreforlearning.redeemer.ab.ca",
	"www.albertahomeeducation.ca":    "centreforlearning.redeemer.ab.ca",
	"myprps.ca":                      "www.myprps.com",
	"www.myprps.ca":                  "www.myprps.com",
	"myprps.com":                     "www.myprps.com",
	"badlandsbaseballacademy.ca":     "baseballacademy.myprps.com",
	"www.badlandsbaseballacademy.ca": "baseballacademy.myprps.com",
	"sabaseballacademy.com":          "sabaseballacademy.myprps.com",
	"www.sabaseballacademy.com":      "sabaseballacademy.myprps.com",
	"sabaseballacademy.myprps.com":   "www.myprps.com",
	"couleecollegiate.com":           "couleecollegiate.myprps.com",
	"couleecollegiate.ca":            "couleecollegiate.myprps.com",
	"www.couleecollegiate.com":       "couleecollegiate.myprps.com",
	"www.couleecollegiate.ca":        "couleecollegiate.myprps.com",
	"www.southalbertahockey.com":     "hockeyacademy.myprps.com",
	"southalbertahockey.com":         "hockeyacademy.myprps.com",
	"cssd.ab.ca":                     "www.cssd.ab.ca",
	"fmcschools.ca":                  "www.fmcschools.ca",
	"grasslands.ab.ca":               "www.grasslands.ab.ca",
	"imagineeverything.ca":           "www.imagineeverything.ca",
	"imagineeverything.com":          "www.imagineeverything.com",
	"livingskysd.ca":                 "www.livingskysd.ca",
	"mhpsd.ca":                       "www.mhpsd.ca",
	"peelschools.org":                "www.peelschools.org",
	"sharethesmile.ca":               "www.sharethesmile.ca",
	"stopr.ca":                       "www.stopr.ca",
	"studyinpeel.com":                "www.studyinpeel.com",
	"werisetogether.ca":              "www.werisetogether.ca",
	"peellearningfoundation.org":     "www.peellearningfoundation.org",
	"ecsd.net":                       "www.ecsd.net",
	"readin.ca":                      "www.readin.ca",
	"sd22.bc.ca":                     "www.sd22.bc.ca",
	"sd22learns.ca":                  "www.sd22learns.ca",
	"vernoninternational.ca":         "www.vernoninternational.ca",
	"hpsd.ca":                        "www.hpsd.ca",
	"sd23.bc.ca":                     "www.sd23.bc.ca",
	"www.ske.sd23.bc.ca":             "ske.sd23.bc.ca",
	"www.international.sd23.bc.ca":   "international.sd23.bc.ca",
	"hfcrd.ab.ca":                    "www.hfcrd.ab.ca",
	"limestone.on.ca":                "www.limestone.on.ca",
	"seeyourselfinlimestone.ca":      "www.limestone.on.ca",
	"www.seeyourselfinlimestone.ca":  "www.limestone.on.ca",
	"christtheteacher.ca":            "www.christtheteacher.ca",
	"nwsd.ca":                        "www.nwsd.ca",
	"holyspirit.ab.ca":               "www.holyspirit.ab.ca",
	"ecacs.ca":                       "www.ecacs.ca",
	"sd91.bc.ca":                     "www.sd91.bc.ca",
	"blackgold.ca":                   "www.blackgold.ca",
	"sd53.bc.ca":                     "www.sd53.bc.ca",
	"allboys.cbe.ab.ca":              "www.cbe.ab.ca",
}

func getToAddress(host string) string {
	if to, ok := mappedHosts[host]; ok {
		return "https://" + to
	}
	if _, ok := validHosts[host]; ok {
		return "https://www." + host
	}
	return ""
}

func handleRequest(_ context.Context, request events.ALBTargetGroupRequest) (events.ALBTargetGroupResponse, error) {
	redirectTo := getToAddress(request.Headers["host"])
	if len(redirectTo) == 0 {
		return events.ALBTargetGroupResponse{Body: "Content Manager Redirect Lambda: Unknown Domain", StatusCode: 200, StatusDescription: "200 OK", IsBase64Encoded: false, Headers: map[string]string{
			"Content-Type": "text/plain",
		}}, nil
	}

	redirectTo = redirectTo + request.Path

	if len(request.QueryStringParameters) > 0 {
		queryParts := make([]string, 0, len(request.QueryStringParameters))
		for key, value := range request.QueryStringParameters {
			queryParts = append(queryParts, url.QueryEscape(key)+"="+url.QueryEscape(value))
		}
		queryString := strings.Join(queryParts, "&")
		redirectTo += "?" + queryString
	}

	return events.ALBTargetGroupResponse{Body: fmt.Sprintf("Redirecting to %s", redirectTo), StatusCode: 301, StatusDescription: "301 Moved Permanently", IsBase64Encoded: false, Headers: map[string]string{
		"Location":                  redirectTo,
		"Content-Type":              "text/plain",
		"Strict-Transport-Security": "max-age=31536000; includeSubDomains;",
		"Content-Security-Policy":   "default-src https:",
		"X-Ie-App-Version":          "lambda",
	}}, nil
}

func main() {
	lambda.Start(handleRequest)
}

// TODO: Remove
var validHosts = map[string]interface{}{
	"badlandsbaseballacademy.ca": struct{}{},
	"couleecollegiate.ca":        struct{}{},
	"couleecollegiate.com":       struct{}{},
	"myprps.ca":                  struct{}{},
	"myprps.com":                 struct{}{},
	"southalbertahockey.com":     struct{}{},

	"cssd.ab.ca": struct{}{},

	"fmcschools.ca": struct{}{},

	"grasslands.ab.ca": struct{}{},

	"imagineeverything.ca":  struct{}{},
	"imagineeverything.com": struct{}{},

	"livingskysd.ca": struct{}{},

	"mhpsd.ca": struct{}{},

	"peelschools.org":   struct{}{},
	"sharethesmile.ca":  struct{}{},
	"stopr.ca":          struct{}{},
	"studyinpeel.com":   struct{}{},
	"werisetogether.ca": struct{}{},

	"redeemer.ab.ca": struct{}{},

	"ecsd.net":  struct{}{},
	"readin.ca": struct{}{},

	"sd22.bc.ca":                 struct{}{},
	"sd22learns.ca":              struct{}{},
	"vernoninternational.ca":     struct{}{},
	"peellearningfoundation.org": struct{}{},

	"hpsd.ca":    struct{}{},
	"sd23.bc.ca": struct{}{},
}
