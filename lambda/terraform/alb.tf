
import {
  to = aws_lb.cm_alb["CM-Redirect-ALB"]
  id = "arn:aws:elasticloadbalancing:ca-central-1:793554545599:loadbalancer/app/CM-Redirect-ALB/28f500b37a13c722"
}

resource "aws_lb" "cm_alb" {
    for_each           = var.alb_names
    name               = each.value
    internal           = false
    load_balancer_type = "application"
    security_groups    = var.security_grp
    subnets            = var.subnets

    tags = merge (
        {},
        local.common_tags
    )
}


import {
  to = aws_lb_target_group.lambda["CM-Redirect-ALB"]
  id = "arn:aws:elasticloadbalancing:ca-central-1:793554545599:targetgroup/CM-Redirect-ALB/e58db0b0b29765b5"
}

resource "aws_lb_target_group" "lambda" {
  for_each    = var.alb_names
  name        = each.value
  target_type = "lambda"

  health_check {
    enabled = true
    healthy_threshold = 2
    unhealthy_threshold = 2
    timeout = 5
    interval = 30
    path = "/"
    matcher = "200,301"
  }

  tags = merge(
    {
        Name = local.lambda_tg_name
    },
    local.common_tags
  )
}

import {
  to = aws_lambda_permission.alb
  id = "CM-Domain-Redirect/AllowExecutionFromALB"

}

resource "aws_lambda_permission" "alb" {
  statement_id  = "AllowExecutionFromALB"
  action        = "lambda:InvokeFunction"
  function_name =  aws_lambda_function.function.arn
  principal     = "elasticloadbalancing.amazonaws.com"
  source_arn    = aws_lb_target_group.lambda["CM-Redirect-ALB"].arn
}

resource "aws_lb_target_group_attachment" "alb" {
  target_group_arn = aws_lb_target_group.lambda["CM-Redirect-ALB"].arn
  target_id        = aws_lambda_function.function.arn
  depends_on       = [aws_lambda_permission.alb]
}

import {
  to = aws_lb_listener.alb_listener_http
  id = "arn:aws:elasticloadbalancing:ca-central-1:793554545599:listener/app/CM-Redirect-ALB/28f500b37a13c722/66491857e85ca346"
}

# Listener rule for HTTP traffic on first item in ALB collection
resource "aws_lb_listener" "alb_listener_http" {
   load_balancer_arn    = aws_lb.cm_alb["CM-Redirect-ALB"].id
   port                 = "80"
   protocol             = "HTTP"
   default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.lambda["CM-Redirect-ALB"].id
  }
}

import {
  to = aws_lb_listener.alb_listner_https
  id = "arn:aws:elasticloadbalancing:ca-central-1:793554545599:listener/app/CM-Redirect-ALB/28f500b37a13c722/eb0d10ed8be5c896"
}

# Listener rule for HTTPs traffic on first item in ALB collection
resource "aws_lb_listener" "alb_listner_https" {
  load_balancer_arn = aws_lb.cm_alb["CM-Redirect-ALB"].id
  port              = "443"
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-TLS13-1-2-2021-06"
  certificate_arn   = local.default_cert_arn
  default_action {
     type             = "forward"
     target_group_arn = aws_lb_target_group.lambda["CM-Redirect-ALB"].id
  }
}


import {
  to = aws_lb_listener_certificate.imagineeverything_ca_alb
  id = "arn:aws:elasticloadbalancing:ca-central-1:793554545599:listener/app/CM-Redirect-ALB/28f500b37a13c722/eb0d10ed8be5c896_arn:aws:acm:ca-central-1:793554545599:certificate/94ce5362-911a-4fc6-ad40-edc8ecca1b63"
}
resource "aws_lb_listener_certificate" "imagineeverything_ca_alb" {
  listener_arn = aws_lb_listener.alb_listner_https.arn
  certificate_arn = "arn:aws:acm:ca-central-1:793554545599:certificate/94ce5362-911a-4fc6-ad40-edc8ecca1b63"
}

import {
  to = aws_lb_listener_certificate.fmcschools_ca_alb
  id = "arn:aws:elasticloadbalancing:ca-central-1:793554545599:listener/app/CM-Redirect-ALB/28f500b37a13c722/eb0d10ed8be5c896_arn:aws:acm:ca-central-1:793554545599:certificate/f218b084-fde6-46a3-bcf0-4d472bbde1b9"
}
resource "aws_lb_listener_certificate" "fmcschools_ca_alb" {
  listener_arn = aws_lb_listener.alb_listner_https.arn
  certificate_arn = "arn:aws:acm:ca-central-1:793554545599:certificate/f218b084-fde6-46a3-bcf0-4d472bbde1b9"
}

import {
  to = aws_lb_listener_certificate.grasslands_ca_alb
  id = "arn:aws:elasticloadbalancing:ca-central-1:793554545599:listener/app/CM-Redirect-ALB/28f500b37a13c722/eb0d10ed8be5c896_arn:aws:acm:ca-central-1:793554545599:certificate/e779b3da-cb79-47cf-8287-a3eb0603534b"
}
resource "aws_lb_listener_certificate" "grasslands_ca_alb" {
  listener_arn = aws_lb_listener.alb_listner_https.arn
  certificate_arn = "arn:aws:acm:ca-central-1:793554545599:certificate/e779b3da-cb79-47cf-8287-a3eb0603534b"
}

import {
  to = aws_lb_listener_certificate.mhpsd_ca_alb
  id = "arn:aws:elasticloadbalancing:ca-central-1:793554545599:listener/app/CM-Redirect-ALB/28f500b37a13c722/eb0d10ed8be5c896_arn:aws:acm:ca-central-1:793554545599:certificate/f329e514-758c-423a-8d54-0a26972c9adb"
}
resource "aws_lb_listener_certificate" "mhpsd_ca_alb" {
  listener_arn = aws_lb_listener.alb_listner_https.arn
  certificate_arn = "arn:aws:acm:ca-central-1:793554545599:certificate/f329e514-758c-423a-8d54-0a26972c9adb"
}

import {
  to = aws_lb_listener_certificate.redeemer_ca_alb
  id = "arn:aws:elasticloadbalancing:ca-central-1:793554545599:listener/app/CM-Redirect-ALB/28f500b37a13c722/eb0d10ed8be5c896_arn:aws:acm:ca-central-1:793554545599:certificate/73a6d045-5ce5-4627-a6c0-ea213de810b4"
}
resource "aws_lb_listener_certificate" "redeemer_ca_alb" {
  listener_arn = aws_lb_listener.alb_listner_https.arn
  certificate_arn = "arn:aws:acm:ca-central-1:793554545599:certificate/73a6d045-5ce5-4627-a6c0-ea213de810b4"
}

import {
  to = aws_lb_listener_certificate.myprps_ca_alb
  id = "arn:aws:elasticloadbalancing:ca-central-1:793554545599:listener/app/CM-Redirect-ALB/28f500b37a13c722/eb0d10ed8be5c896_arn:aws:acm:ca-central-1:793554545599:certificate/51f6025e-f31b-45bf-8d86-39fe461c5848"
}
resource "aws_lb_listener_certificate" "myprps_ca_alb" {
  listener_arn = aws_lb_listener.alb_listner_https.arn
  certificate_arn = "arn:aws:acm:ca-central-1:793554545599:certificate/51f6025e-f31b-45bf-8d86-39fe461c5848"
}

import {
  to = aws_lb_listener_certificate.sd22_bc_ca_alb
  id = "arn:aws:elasticloadbalancing:ca-central-1:793554545599:listener/app/CM-Redirect-ALB/28f500b37a13c722/eb0d10ed8be5c896_arn:aws:acm:ca-central-1:793554545599:certificate/3c1ad341-0485-48ac-88f7-9b15936b519c"
}
resource "aws_lb_listener_certificate" "sd22_bc_ca_alb" {
  listener_arn = aws_lb_listener.alb_listner_https.arn
  certificate_arn = "arn:aws:acm:ca-central-1:793554545599:certificate/3c1ad341-0485-48ac-88f7-9b15936b519c"
}


import {
  to = aws_lb_listener_certificate.peelschools_ca_alb
  id = "arn:aws:elasticloadbalancing:ca-central-1:793554545599:listener/app/CM-Redirect-ALB/28f500b37a13c722/eb0d10ed8be5c896_arn:aws:acm:ca-central-1:793554545599:certificate/cffe2203-24e9-4b67-94f9-a52e27a4b46b"
}
resource "aws_lb_listener_certificate" "peelschools_ca_alb" {
  listener_arn = aws_lb_listener.alb_listner_https.arn
  certificate_arn = "arn:aws:acm:ca-central-1:793554545599:certificate/cffe2203-24e9-4b67-94f9-a52e27a4b46b"
}

import {
  to = aws_lb_listener_certificate.hpsd_ca_alb
  id = "arn:aws:elasticloadbalancing:ca-central-1:793554545599:listener/app/CM-Redirect-ALB/28f500b37a13c722/eb0d10ed8be5c896_arn:aws:acm:ca-central-1:793554545599:certificate/27ed4ff9-d4f9-47f9-9206-ffaa4ba6ff5a"
}
resource "aws_lb_listener_certificate" "hpsd_ca_alb" {
  listener_arn = aws_lb_listener.alb_listner_https.arn
  certificate_arn = "arn:aws:acm:ca-central-1:793554545599:certificate/27ed4ff9-d4f9-47f9-9206-ffaa4ba6ff5a"
}

import {
  to = aws_lb_listener_certificate.albertahomeeducation_ca
  id = "arn:aws:elasticloadbalancing:ca-central-1:793554545599:listener/app/CM-Redirect-ALB/28f500b37a13c722/eb0d10ed8be5c896_arn:aws:acm:ca-central-1:793554545599:certificate/8edd92ef-bf8a-484a-98ff-e7b612c72ac5"
}

resource "aws_lb_listener_certificate" "albertahomeeducation_ca" {
  listener_arn = aws_lb_listener.alb_listner_https.arn
  certificate_arn = "arn:aws:acm:ca-central-1:793554545599:certificate/8edd92ef-bf8a-484a-98ff-e7b612c72ac5"
}
import {
  to = aws_lb_listener_certificate.sd23_bc_ca_alb
  id = "arn:aws:elasticloadbalancing:ca-central-1:793554545599:listener/app/CM-Redirect-ALB/28f500b37a13c722/eb0d10ed8be5c896_arn:aws:acm:ca-central-1:793554545599:certificate/f99a32d9-d215-4cbf-9504-4b2694d13596"
}

resource "aws_lb_listener_certificate" "sd23_bc_ca_alb" {
  listener_arn = aws_lb_listener.alb_listner_https.arn
  certificate_arn = "arn:aws:acm:ca-central-1:793554545599:certificate/f99a32d9-d215-4cbf-9504-4b2694d13596"
}
import {
  to = aws_lb_listener_certificate.hfcrd_bc_ca_alb
  id = "arn:aws:elasticloadbalancing:ca-central-1:793554545599:listener/app/CM-Redirect-ALB/28f500b37a13c722/eb0d10ed8be5c896_arn:aws:acm:ca-central-1:793554545599:certificate/8466ea30-e57b-42db-b1d4-fddfdcd47580"
}

resource "aws_lb_listener_certificate" "hfcrd_bc_ca_alb" {
  listener_arn = aws_lb_listener.alb_listner_https.arn
  certificate_arn = "arn:aws:acm:ca-central-1:793554545599:certificate/8466ea30-e57b-42db-b1d4-fddfdcd47580"
}

import {
  to = aws_lb_listener_certificate.limestone_on_ca_alb
  id = "arn:aws:elasticloadbalancing:ca-central-1:793554545599:listener/app/CM-Redirect-ALB/28f500b37a13c722/eb0d10ed8be5c896_arn:aws:acm:ca-central-1:793554545599:certificate/c764d7b9-95ab-41d6-9c84-6fed0821b249"
}
resource "aws_lb_listener_certificate" "limestone_on_ca_alb" {
  listener_arn = aws_lb_listener.alb_listner_https.arn
  certificate_arn = "arn:aws:acm:ca-central-1:793554545599:certificate/c764d7b9-95ab-41d6-9c84-6fed0821b249"
}

import {
  to = aws_lb_listener_certificate.christtheteacher_ca_ab_alb
  id = "arn:aws:elasticloadbalancing:ca-central-1:793554545599:listener/app/CM-Redirect-ALB/28f500b37a13c722/eb0d10ed8be5c896_arn:aws:acm:ca-central-1:793554545599:certificate/b917408c-abfd-46d6-a7c3-a2f1717023b3"
}
resource "aws_lb_listener_certificate" "christtheteacher_ca_ab_alb" {
  listener_arn = aws_lb_listener.alb_listner_https.arn
  certificate_arn = "arn:aws:acm:ca-central-1:793554545599:certificate/b917408c-abfd-46d6-a7c3-a2f1717023b3"
}

import {
  to = aws_lb_listener_certificate.nwsd_ca_sk_alb
  id = "arn:aws:elasticloadbalancing:ca-central-1:793554545599:listener/app/CM-Redirect-ALB/28f500b37a13c722/eb0d10ed8be5c896_arn:aws:acm:ca-central-1:793554545599:certificate/26df9e95-710e-4e54-a05a-e3cd373eafdf"
}
resource "aws_lb_listener_certificate" "nwsd_ca_sk_alb" {
  listener_arn = aws_lb_listener.alb_listner_https.arn
  certificate_arn = "arn:aws:acm:ca-central-1:793554545599:certificate/26df9e95-710e-4e54-a05a-e3cd373eafdf"
}

import {
  to = aws_lb_listener_certificate.holyspirit_ab_ca
  id = "arn:aws:elasticloadbalancing:ca-central-1:793554545599:listener/app/CM-Redirect-ALB/28f500b37a13c722/eb0d10ed8be5c896_arn:aws:acm:ca-central-1:793554545599:certificate/549c33bc-b33e-4d13-befe-0df555b70e4b"
}
resource "aws_lb_listener_certificate" "holyspirit_ab_ca" {
  listener_arn = aws_lb_listener.alb_listner_https.arn
  certificate_arn = "arn:aws:acm:ca-central-1:793554545599:certificate/549c33bc-b33e-4d13-befe-0df555b70e4b"
}

import {
  to = aws_lb_listener_certificate.ecacs_ab_ca
  id = "arn:aws:elasticloadbalancing:ca-central-1:793554545599:listener/app/CM-Redirect-ALB/28f500b37a13c722/eb0d10ed8be5c896_arn:aws:acm:ca-central-1:793554545599:certificate/e38f0a64-ffb2-40f9-bcf4-ad5805c487a8"
}
resource "aws_lb_listener_certificate" "ecacs_ab_ca" {
  listener_arn = aws_lb_listener.alb_listner_https.arn
  certificate_arn = "arn:aws:acm:ca-central-1:793554545599:certificate/e38f0a64-ffb2-40f9-bcf4-ad5805c487a8"
}

import {
  to = aws_lb_listener_certificate.prrd8_ab_ca_consolidated
  id = "arn:aws:elasticloadbalancing:ca-central-1:793554545599:listener/app/CM-Redirect-ALB/28f500b37a13c722/eb0d10ed8be5c896_arn:aws:acm:ca-central-1:793554545599:certificate/b2c764c1-651b-44e7-81b9-4f87012b68e6"
}
resource "aws_lb_listener_certificate" "prrd8_ab_ca_consolidated" {
  listener_arn = aws_lb_listener.alb_listner_https.arn
  certificate_arn = "arn:aws:acm:ca-central-1:793554545599:certificate/b2c764c1-651b-44e7-81b9-4f87012b68e6"
}

import {
  to = aws_lb_listener_certificate.sd91_bc_ca
  id = "arn:aws:elasticloadbalancing:ca-central-1:793554545599:listener/app/CM-Redirect-ALB/28f500b37a13c722/eb0d10ed8be5c896_arn:aws:acm:ca-central-1:793554545599:certificate/f4736d11-1fb3-4896-9c57-09d423f8a6a5"
}
resource "aws_lb_listener_certificate" "sd91_bc_ca" {
  listener_arn = aws_lb_listener.alb_listner_https.arn
  certificate_arn = "arn:aws:acm:ca-central-1:793554545599:certificate/f4736d11-1fb3-4896-9c57-09d423f8a6a5"
}

import {
  to = aws_lb_listener_certificate.ecsd_net
  id = "arn:aws:elasticloadbalancing:ca-central-1:793554545599:listener/app/CM-Redirect-ALB/28f500b37a13c722/eb0d10ed8be5c896_arn:aws:acm:ca-central-1:793554545599:certificate/5bf53f1c-2254-4fae-b9db-8930b14e28bf"
}
resource "aws_lb_listener_certificate" "ecsd_net" {
  listener_arn = aws_lb_listener.alb_listner_https.arn
  certificate_arn = "arn:aws:acm:ca-central-1:793554545599:certificate/5bf53f1c-2254-4fae-b9db-8930b14e28bf"
}

import {
  to = aws_lb_listener_certificate.blackgold_ca
  id = "arn:aws:elasticloadbalancing:ca-central-1:793554545599:listener/app/CM-Redirect-ALB/28f500b37a13c722/eb0d10ed8be5c896_arn:aws:acm:ca-central-1:793554545599:certificate/fd2b71b5-a92e-4a2b-987e-c19bc86eae72"
}
resource "aws_lb_listener_certificate" "blackgold_ca" {
  listener_arn = aws_lb_listener.alb_listner_https.arn
  certificate_arn = "arn:aws:acm:ca-central-1:793554545599:certificate/fd2b71b5-a92e-4a2b-987e-c19bc86eae72"
}

import {
  to = aws_lb_listener_certificate.prrd8_sabaseballacademy_com
  id = "arn:aws:elasticloadbalancing:ca-central-1:793554545599:listener/app/CM-Redirect-ALB/28f500b37a13c722/eb0d10ed8be5c896_arn:aws:acm:ca-central-1:793554545599:certificate/850ff609-a5d4-410a-9897-09b42c30997d"
}
resource "aws_lb_listener_certificate" "prrd8_sabaseballacademy_com" {
  listener_arn = aws_lb_listener.alb_listner_https.arn
  certificate_arn = "arn:aws:acm:ca-central-1:793554545599:certificate/850ff609-a5d4-410a-9897-09b42c30997d"
}


import {
  to = aws_lb_listener_certificate.sd53_bc_ca
  id = "arn:aws:elasticloadbalancing:ca-central-1:793554545599:listener/app/CM-Redirect-ALB/28f500b37a13c722/eb0d10ed8be5c896_arn:aws:acm:ca-central-1:793554545599:certificate/b08d192d-853c-48d1-9760-3a28412a47a4"
}
resource "aws_lb_listener_certificate" "sd53_bc_ca" {
  listener_arn = aws_lb_listener.alb_listner_https.arn
  certificate_arn = "arn:aws:acm:ca-central-1:793554545599:certificate/b08d192d-853c-48d1-9760-3a28412a47a4"
}

resource "aws_lb_listener_certificate" "cbe_ab_ca" {
  listener_arn    = aws_lb_listener.alb_listner_https.arn
  certificate_arn = "arn:aws:acm:ca-central-1:793554545599:certificate/fc42a30a-b4cc-4556-a189-6ed993032d00"
}